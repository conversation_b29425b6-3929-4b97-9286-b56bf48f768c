import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { toast } from 'sonner';
import { useRedirectLoopPrevention } from '@/utils/redirectLoopPrevention';
import { useAuth } from '@/contexts/AuthContext';

interface AuthRouteProps {
  children: React.ReactNode;
}

/**
 * AuthRoute component redirects authenticated users away from auth pages (login, register, etc.)
 * If a user is already logged in, they will be redirected to the appropriate dashboard
 * Includes loop prevention logic
 *
 * Updated to use AuthContext instead of direct localStorage access to prevent state sync issues
 */
const AuthRoute: React.FC<AuthRouteProps> = ({ children }) => {
  const location = useLocation();
  const { canRedirect } = useRedirectLoopPrevention();
  const { user, isAuthenticated, isLoading } = useAuth();

  // Show loading state while auth is being determined
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Only redirect if user is authenticated and we have user data
  if (isAuthenticated && user) {
    const isStaff = user.is_staff || user.is_superuser;
    const targetPath = isStaff ? '/graduate-admin' : '/dashboard';

    // Check if redirect is allowed (prevents loops)
    if (!canRedirect(location.pathname, targetPath)) {
      console.warn(`AuthRoute: Redirect loop prevented from ${location.pathname} to ${targetPath}`);
      return <>{children}</>;
    }

    // Redirect to appropriate dashboard based on user role
    if (isStaff) {
      console.log('AuthRoute: Redirecting staff user to /graduate-admin');
      return <Navigate to="/graduate-admin" replace />;
    } else {
      console.log('AuthRoute: Redirecting regular user to /dashboard');
      return <Navigate to="/dashboard" replace />;
    }
  }

  return <>{children}</>;
};

export default AuthRoute;
