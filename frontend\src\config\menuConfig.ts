import { 
  Home, 
  UserCheck, 
  FileText, 
  Settings, 
  Users, 
  Shield, 
  MessageSquare, 
  Download,
  BarChart3,
  Building,
  GraduationCap,
  BookOpen,
  Calendar,
  FileCheck,
  Mail,
  Phone,
  UserPlus,
  Key,
  Eye,
  AlertTriangle
} from 'lucide-react';

export interface MenuItem {
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  path?: string;
  submenu?: boolean;
  items?: MenuItem[];
  requiredPermissions?: string[];
  requiredFeature?: string;
  requireSuperuser?: boolean;
  active?: boolean;
}

/**
 * Permission-based menu configuration
 * Each menu item can specify required permissions or features
 */
export const getMenuConfig = (): MenuItem[] => [
  {
    title: 'Dashboard',
    icon: Home,
    submenu: true,
    items: [
      {
        title: 'Graduation Dashboard',
        icon: BarChart3,
        path: '/graduate-admin?tab=dashboard',
        // Dashboard is accessible to all staff users
      },
      {
        title: 'Application Dashboard',
        icon: FileText,
        path: '/graduate-admin?tab=application-dashboard',
        requiredFeature: 'alumni-applications',
      },
      {
        title: 'Service Fee Dashboard',
        icon: BarChart3,
        path: '/graduate-admin?tab=service-fee-dashboard',
        requiredFeature: 'alumni-applications',
      }
    ]
  },
  {
    title: 'Graduate Verification',
    icon: UserCheck,
    submenu: true,
    items: [
      {
        title: 'Manage Graduates',
        icon: GraduationCap,
        path: '/graduate-admin?tab=manage',
        // Basic staff access
      },
      {
        title: 'Manage Colleges',
        icon: Building,
        path: '/graduate-admin?tab=colleges',
        requiredFeature: 'colleges',
      },
      {
        title: 'Manage Departments',
        icon: Building,
        path: '/graduate-admin?tab=departments',
        requiredFeature: 'departments',
      },
      {
        title: 'Graduate Fields of Study',
        icon: BookOpen,
        path: '/graduate-admin?tab=graduate-fields-of-study',
        requiredFeature: 'programs',
      },
      {
        title: 'Manage Admission Classifications',
        icon: FileCheck,
        path: '/graduate-admin?tab=admission-classifications',
        requiredPermissions: ['admission_type.view_admissiontype'],
      },
      {
        title: 'Manage Programs',
        icon: GraduationCap,
        path: '/graduate-admin?tab=programs',
        requiredFeature: 'programs',
      }
    ]
  },
  {
    title: 'Application Portal',
    icon: FileText,
    submenu: true,
    items: [
      {
        title: 'Manage Colleges',
        icon: Building,
        path: '/graduate-admin?tab=application-colleges',
        requiredFeature: 'colleges',
      },
      {
        title: 'Manage Departments',
        icon: Building,
        path: '/graduate-admin?tab=application-departments',
        requiredFeature: 'departments',
      },
      {
        title: 'Manage Admission Types',
        icon: FileCheck,
        path: '/graduate-admin?tab=admission-types',
        requiredFeature: 'admission-types',
      },
      {
        title: 'Manage Programs',
        icon: GraduationCap,
        path: '/graduate-admin?tab=application-programs',
        requiredFeature: 'programs',
      },
      {
        title: 'Manage Study Fields',
        icon: BookOpen,
        path: '/graduate-admin?tab=study-fields',
        requiredFeature: 'programs',
      },
      {
        title: 'Manage Study Programs',
        icon: GraduationCap,
        path: '/graduate-admin?tab=study-programs',
        requiredFeature: 'programs',
      },
      {
        title: 'Manage Years',
        icon: Calendar,
        path: '/graduate-admin?tab=years',
        requiredFeature: 'programs',
      },
      {
        title: 'Manage Terms',
        icon: Calendar,
        path: '/graduate-admin?tab=terms',
        requiredFeature: 'programs',
      },
      {
        title: 'Application Information',
        icon: FileText,
        path: '/graduate-admin?tab=application-information',
        requiredFeature: 'alumni-applications',
      },
      {
        title: 'Registration Periods',
        icon: Calendar,
        path: '/graduate-admin?tab=registration-periods',
        requiredFeature: 'programs',
      }
    ]
  },
  {
    title: 'System Settings',
    icon: Settings,
    submenu: true,
    items: [
      {
        title: 'Settings',
        icon: Settings,
        path: '/graduate-admin?tab=settings',
        requireSuperuser: true,
      },
      {
        title: 'Downloadable Management',
        icon: Download,
        path: '/graduate-admin?tab=downloadable-management',
        requireSuperuser: true,
      }
    ]
  },
  {
    title: 'User Management',
    icon: Users,
    submenu: true,
    requiredFeature: 'users',
    items: [
      {
        title: 'Users',
        icon: Users,
        path: '/graduate-admin?tab=users',
        requiredFeature: 'users',
      },
      {
        title: 'Roles',
        icon: Key,
        path: '/graduate-admin?tab=roles',
        requiredFeature: 'roles',
      },
      {
        title: 'Permissions',
        icon: Shield,
        path: '/graduate-admin?tab=permissions',
        requiredFeature: 'roles',
      },
      {
        title: 'Role Management',
        icon: UserPlus,
        path: '/graduate-admin?tab=role-management',
        requiredFeature: 'roles',
      },
      {
        title: 'Authentication Management',
        icon: Key,
        path: '/graduate-admin?tab=authentication-management',
        requireSuperuser: true,
      }
    ]
  },
  {
    title: 'Officials',
    icon: Shield,
    submenu: true,
    items: [
      {
        title: 'Certificate Types',
        icon: FileCheck,
        path: '/graduate-admin?tab=certificate-types',
        // Basic staff access
      },
      {
        title: 'Official Certificates',
        icon: FileCheck,
        path: '/graduate-admin?tab=official-certificates',
        // Basic staff access
      }
    ]
  },
  {
    title: 'Services',
    icon: Settings,
    submenu: true,
    items: [
      {
        title: 'Service Types',
        icon: Settings,
        path: '/graduate-admin?tab=service-types',
        // Basic staff access
      },
      {
        title: 'Document Types',
        icon: FileText,
        path: '/graduate-admin?tab=document-types',
        // Basic staff access
      },
      {
        title: 'Alumni Applications',
        icon: GraduationCap,
        path: '/graduate-admin?tab=alumni-applications',
        requiredFeature: 'alumni-applications',
      }
    ]
  },
  {
    title: 'Communication',
    icon: MessageSquare,
    submenu: true,
    items: [
      {
        title: 'Announcements',
        icon: MessageSquare,
        path: '/graduate-admin?tab=announcements',
        // Basic staff access
      },
      {
        title: 'Email Notifications',
        icon: Mail,
        path: '/graduate-admin?tab=email-notifications',
        // Basic staff access
      },
      {
        title: 'SMS Notifications',
        icon: Phone,
        path: '/graduate-admin?tab=sms-notifications',
        // Basic staff access
      },
      {
        title: 'Message Center',
        icon: MessageSquare,
        path: '/graduate-admin?tab=message-center',
        // Basic staff access
      }
    ]
  },
  {
    title: 'Security',
    icon: Shield,
    submenu: true,
    requireSuperuser: true,
    items: [
      {
        title: 'Security Monitor',
        icon: Eye,
        path: '/graduate-admin?tab=security-monitor',
        requireSuperuser: true,
      },
      {
        title: 'User Audit',
        icon: Users,
        path: '/graduate-admin?tab=user-audit',
        requireSuperuser: true,
      },
      {
        title: 'Security Debug',
        icon: AlertTriangle,
        path: '/graduate-admin?tab=security-debug',
        requireSuperuser: true,
      },
      {
        title: 'RBAC Test',
        icon: Shield,
        path: '/graduate-admin?tab=rbac-test',
        requireSuperuser: true,
      }
    ]
  }
];

export default getMenuConfig;
