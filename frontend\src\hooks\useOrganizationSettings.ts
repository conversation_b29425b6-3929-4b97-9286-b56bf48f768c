import { useState, useEffect } from 'react';
import { settingsAPI } from '@/services/api';

interface OrganizationSettings {
  systemName: string;
  organizationName: string;
  primaryColor: string;
  secondaryColor: string;
  copyright: string;
  headerLogoUrl: string | null;
  footerLogoUrl: string | null;
}

// Global cache to prevent multiple API calls
let globalSettingsCache: OrganizationSettings | null = null;
let globalCacheTimestamp: number = 0;
let globalLoadingPromise: Promise<OrganizationSettings> | null = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Hook to get organization settings with global caching
 * Prevents multiple API calls when used across different components
 */
export const useOrganizationSettings = () => {
  const [settings, setSettings] = useState<OrganizationSettings | null>(globalSettingsCache);
  const [isLoading, setIsLoading] = useState(!globalSettingsCache);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSettings = async () => {
      const now = Date.now();
      
      // If we have fresh cached data, use it
      if (globalSettingsCache && (now - globalCacheTimestamp) < CACHE_DURATION) {
        setSettings(globalSettingsCache);
        setIsLoading(false);
        return;
      }

      // If there's already a loading promise, wait for it
      if (globalLoadingPromise) {
        try {
          const cachedSettings = await globalLoadingPromise;
          setSettings(cachedSettings);
          setIsLoading(false);
          return;
        } catch (err) {
          // If the promise failed, we'll try again below
        }
      }

      // Create new loading promise
      globalLoadingPromise = loadSettingsFromAPI();
      
      try {
        setIsLoading(true);
        setError(null);
        
        const settingsData = await globalLoadingPromise;
        
        // Update global cache
        globalSettingsCache = settingsData;
        globalCacheTimestamp = now;
        
        setSettings(settingsData);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to load settings';
        setError(errorMessage);
        console.error('Error fetching organization settings:', err);
        
        // Try to use any existing cached data as fallback
        if (globalSettingsCache) {
          setSettings(globalSettingsCache);
        }
      } finally {
        setIsLoading(false);
        globalLoadingPromise = null;
      }
    };

    fetchSettings();
  }, []);

  return { settings, isLoading, error, refetch: () => {
    // Clear cache and refetch
    globalSettingsCache = null;
    globalCacheTimestamp = 0;
    setSettings(null);
    setIsLoading(true);
    setError(null);
  }};
};

/**
 * Load settings from API with proper error handling
 */
const loadSettingsFromAPI = async (): Promise<OrganizationSettings> => {
  try {
    const response = await settingsAPI.getPublicOrganizationSettings();
    
    if (!response.data) {
      throw new Error('No settings data received from API');
    }

    // Process logo URLs to ensure they're absolute
    const processLogoUrl = (url: string | null | undefined): string | null => {
      if (!url) return null;
      
      if (url.startsWith('http')) {
        return url;
      }
      
      const baseUrl = import.meta.env.VITE_BACKEND_MEDIA_URL || 'http://localhost:8000';
      return `${baseUrl}${url}`;
    };

    return {
      systemName: response.data.system_name || '',
      organizationName: response.data.organization || '',
      primaryColor: response.data.primary_color || '#1a73c0',
      secondaryColor: response.data.secondary_color || '#f5f5f5',
      copyright: response.data.copyright || '',
      headerLogoUrl: processLogoUrl(response.data.header_logo_url),
      footerLogoUrl: processLogoUrl(response.data.footer_logo_url),
    };
  } catch (error) {
    // Try to get from localStorage as fallback
    try {
      const cachedSettings = localStorage.getItem('organizationSettings');
      if (cachedSettings) {
        const parsed = JSON.parse(cachedSettings);
        return {
          systemName: parsed.systemName || '',
          organizationName: parsed.organizationName || '',
          primaryColor: parsed.primaryColor || '#1a73c0',
          secondaryColor: parsed.secondaryColor || '#f5f5f5',
          copyright: parsed.copyright || '',
          headerLogoUrl: parsed.headerLogoUrl || null,
          footerLogoUrl: parsed.footerLogoUrl || null,
        };
      }
    } catch (parseError) {
      console.error('Failed to parse cached settings:', parseError);
    }
    
    throw error;
  }
};

/**
 * Hook for components that only need basic settings (system name, org name)
 */
export const useBasicOrganizationSettings = () => {
  const { settings, isLoading, error } = useOrganizationSettings();
  
  return {
    systemName: settings?.systemName || '',
    organizationName: settings?.organizationName || '',
    isLoading,
    error
  };
};

/**
 * Hook for components that need styling settings
 */
export const useOrganizationStyling = () => {
  const { settings, isLoading, error } = useOrganizationSettings();
  
  return {
    primaryColor: settings?.primaryColor || '#1a73c0',
    secondaryColor: settings?.secondaryColor || '#f5f5f5',
    headerLogoUrl: settings?.headerLogoUrl,
    footerLogoUrl: settings?.footerLogoUrl,
    isLoading,
    error
  };
};

/**
 * Clear the global cache (useful for testing or when settings are updated)
 */
export const clearOrganizationSettingsCache = () => {
  globalSettingsCache = null;
  globalCacheTimestamp = 0;
  globalLoadingPromise = null;
};

export default useOrganizationSettings;
