import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissionCheck } from '@/hooks/usePermissionCheck';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Shield, User, Eye, Plus, Edit, Trash2, RefreshCw } from 'lucide-react';

/**
 * Debug component to show current user permissions
 * Useful for testing the permission system
 */
export const PermissionDebugger: React.FC = () => {
  const { user } = useAuth();
  const { 
    checkFeatureAccess, 
    checkPermissions, 
    permissionCache, 
    featureCache,
    clearCache,
    isLoading 
  } = usePermissionCheck();
  
  const [featureResults, setFeatureResults] = useState<Record<string, any>>({});
  const [isChecking, setIsChecking] = useState(false);

  const features = [
    'admission-types',
    'departments', 
    'programs',
    'colleges',
    'users',
    'roles',
    'alumni-applications'
  ];

  const permissions = [
    'admission_type.view_admissiontype',
    'admission_type.add_admissiontype',
    'admission_type.change_admissiontype',
    'admission_type.delete_admissiontype',
    'department.view_department',
    'department.add_department',
    'program.view_program',
    'program.add_program',
    'auth.view_user',
    'auth.add_user',
  ];

  const checkAllFeatures = async () => {
    setIsChecking(true);
    const results: Record<string, any> = {};
    
    for (const feature of features) {
      try {
        const access = await checkFeatureAccess(feature);
        results[feature] = access;
      } catch (error) {
        results[feature] = { error: error.message };
      }
    }
    
    setFeatureResults(results);
    setIsChecking(false);
  };

  useEffect(() => {
    if (user) {
      checkAllFeatures();
    }
  }, [user]);

  if (!user) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Permission Debugger
          </CardTitle>
          <CardDescription>No user logged in</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Permission Debugger
          </CardTitle>
          <CardDescription>
            Debug information for the current user's permissions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* User Info */}
          <div>
            <h3 className="font-semibold flex items-center gap-2 mb-2">
              <User className="h-4 w-4" />
              User Information
            </h3>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>Username: <Badge variant="outline">{user.username}</Badge></div>
              <div>Staff: <Badge variant={user.is_staff ? "default" : "secondary"}>{user.is_staff ? "Yes" : "No"}</Badge></div>
              <div>Superuser: <Badge variant={user.is_superuser ? "destructive" : "secondary"}>{user.is_superuser ? "Yes" : "No"}</Badge></div>
              <div>Active: <Badge variant={user.is_active ? "default" : "secondary"}>{user.is_active ? "Yes" : "No"}</Badge></div>
            </div>
          </div>

          <Separator />

          {/* Actions */}
          <div className="flex gap-2">
            <Button 
              onClick={checkAllFeatures} 
              disabled={isChecking || isLoading}
              size="sm"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isChecking ? 'animate-spin' : ''}`} />
              Refresh Permissions
            </Button>
            <Button 
              onClick={clearCache} 
              variant="outline"
              size="sm"
            >
              Clear Cache
            </Button>
          </div>

          <Separator />

          {/* Feature Access */}
          <div>
            <h3 className="font-semibold mb-2">Feature Access</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {features.map(feature => {
                const result = featureResults[feature];
                if (!result) return null;

                return (
                  <div key={feature} className="border rounded-lg p-3">
                    <div className="font-medium mb-2 capitalize">
                      {feature.replace('-', ' ')}
                    </div>
                    {result.error ? (
                      <Badge variant="destructive">Error: {result.error}</Badge>
                    ) : (
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <Eye className="h-3 w-3" />
                          <span className="text-xs">View:</span>
                          <Badge variant={result.canView ? "default" : "secondary"} className="text-xs">
                            {result.canView ? "Yes" : "No"}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <Plus className="h-3 w-3" />
                          <span className="text-xs">Add:</span>
                          <Badge variant={result.canAdd ? "default" : "secondary"} className="text-xs">
                            {result.canAdd ? "Yes" : "No"}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <Edit className="h-3 w-3" />
                          <span className="text-xs">Edit:</span>
                          <Badge variant={result.canEdit ? "default" : "secondary"} className="text-xs">
                            {result.canEdit ? "Yes" : "No"}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <Trash2 className="h-3 w-3" />
                          <span className="text-xs">Delete:</span>
                          <Badge variant={result.canDelete ? "default" : "secondary"} className="text-xs">
                            {result.canDelete ? "Yes" : "No"}
                          </Badge>
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          <Separator />

          {/* Permission Cache */}
          <div>
            <h3 className="font-semibold mb-2">Permission Cache</h3>
            <div className="max-h-40 overflow-y-auto">
              {Object.keys(permissionCache).length === 0 ? (
                <p className="text-sm text-gray-500">No permissions cached</p>
              ) : (
                <div className="space-y-1">
                  {Object.entries(permissionCache).map(([permission, hasPermission]) => (
                    <div key={permission} className="flex items-center justify-between text-xs">
                      <span className="font-mono">{permission}</span>
                      <Badge variant={hasPermission ? "default" : "secondary"}>
                        {hasPermission ? "✓" : "✗"}
                      </Badge>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Feature Cache */}
          <div>
            <h3 className="font-semibold mb-2">Feature Cache</h3>
            <div className="max-h-40 overflow-y-auto">
              {Object.keys(featureCache).length === 0 ? (
                <p className="text-sm text-gray-500">No features cached</p>
              ) : (
                <div className="space-y-1">
                  {Object.entries(featureCache).map(([feature, access]) => (
                    <div key={feature} className="text-xs">
                      <span className="font-mono">{feature}:</span>
                      <Badge variant={access.canAccess ? "default" : "secondary"} className="ml-2">
                        {access.canAccess ? "Accessible" : "Restricted"}
                      </Badge>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PermissionDebugger;
