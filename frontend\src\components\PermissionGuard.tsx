import React, { useState, useEffect } from 'react';
import { usePermissionCheck } from '@/hooks/usePermissionCheck';
import { useAuth } from '@/contexts/AuthContext';

interface PermissionGuardProps {
  children: React.ReactNode;
  permission?: string;
  permissions?: string[];
  feature?: string;
  requireAll?: boolean; // If true, requires ALL permissions; if false, requires ANY permission
  fallback?: React.ReactNode;
  showLoading?: boolean;
  onAccessDenied?: () => void;
}

/**
 * Component that conditionally renders children based on user permissions
 * Checks permissions against the Django backend to ensure consistency
 */
export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permission,
  permissions,
  feature,
  requireAll = false,
  fallback = null,
  showLoading = true,
  onAccessDenied,
}) => {
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  const [isChecking, setIsChecking] = useState(true);
  const { 
    checkPermission, 
    checkPermissions, 
    checkFeatureAccess, 
    hasAnyPermission, 
    hasAllPermissions 
  } = usePermissionCheck();
  const { user, isAuthenticated } = useAuth();

  useEffect(() => {
    const checkAccess = async () => {
      if (!isAuthenticated || !user) {
        setHasAccess(false);
        setIsChecking(false);
        return;
      }

      // Superusers have access to everything
      if (user.is_superuser) {
        setHasAccess(true);
        setIsChecking(false);
        return;
      }

      try {
        setIsChecking(true);
        let access = false;

        if (feature) {
          // Check feature access
          const featureAccess = await checkFeatureAccess(feature);
          access = featureAccess.canAccess;
        } else if (permission) {
          // Check single permission
          access = await checkPermission(permission);
        } else if (permissions && permissions.length > 0) {
          // Check multiple permissions
          if (requireAll) {
            access = await hasAllPermissions(permissions);
          } else {
            access = await hasAnyPermission(permissions);
          }
        } else {
          // No permissions specified, allow access for authenticated users
          access = true;
        }

        setHasAccess(access);
        
        if (!access && onAccessDenied) {
          onAccessDenied();
        }
      } catch (error) {
        console.error('Permission check failed:', error);
        setHasAccess(false);
      } finally {
        setIsChecking(false);
      }
    };

    checkAccess();
  }, [
    isAuthenticated,
    user,
    permission,
    permissions,
    feature,
    requireAll,
    checkPermission,
    checkPermissions,
    checkFeatureAccess,
    hasAnyPermission,
    hasAllPermissions,
    onAccessDenied,
  ]);

  // Show loading state
  if (isChecking && showLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-sm text-gray-600">Checking permissions...</span>
      </div>
    );
  }

  // Show fallback or nothing if access denied
  if (hasAccess === false) {
    return <>{fallback}</>;
  }

  // Show children if access granted
  if (hasAccess === true) {
    return <>{children}</>;
  }

  // Default fallback while checking
  return <>{fallback}</>;
};

interface FeatureGuardProps {
  children: React.ReactNode;
  feature: string;
  action?: 'view' | 'add' | 'edit' | 'delete';
  fallback?: React.ReactNode;
  showLoading?: boolean;
}

/**
 * Specialized guard for feature-based access control
 */
export const FeatureGuard: React.FC<FeatureGuardProps> = ({
  children,
  feature,
  action = 'view',
  fallback = null,
  showLoading = true,
}) => {
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  const [isChecking, setIsChecking] = useState(true);
  const { checkFeatureAccess } = usePermissionCheck();
  const { user, isAuthenticated } = useAuth();

  useEffect(() => {
    const checkAccess = async () => {
      if (!isAuthenticated || !user) {
        setHasAccess(false);
        setIsChecking(false);
        return;
      }

      // Superusers have access to everything
      if (user.is_superuser) {
        setHasAccess(true);
        setIsChecking(false);
        return;
      }

      try {
        setIsChecking(true);
        const featureAccess = await checkFeatureAccess(feature);
        
        let access = false;
        switch (action) {
          case 'view':
            access = featureAccess.canView;
            break;
          case 'add':
            access = featureAccess.canAdd;
            break;
          case 'edit':
            access = featureAccess.canEdit;
            break;
          case 'delete':
            access = featureAccess.canDelete;
            break;
          default:
            access = featureAccess.canAccess;
        }

        setHasAccess(access);
      } catch (error) {
        console.error('Feature access check failed:', error);
        setHasAccess(false);
      } finally {
        setIsChecking(false);
      }
    };

    checkAccess();
  }, [isAuthenticated, user, feature, action, checkFeatureAccess]);

  // Show loading state
  if (isChecking && showLoading) {
    return (
      <div className="flex items-center justify-center p-2">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Show children if access granted
  if (hasAccess === true) {
    return <>{children}</>;
  }

  // Show fallback if access denied
  return <>{fallback}</>;
};

interface ConditionalRenderProps {
  condition: boolean;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * Simple conditional render component
 */
export const ConditionalRender: React.FC<ConditionalRenderProps> = ({
  condition,
  children,
  fallback = null,
}) => {
  return condition ? <>{children}</> : <>{fallback}</>;
};

export default PermissionGuard;
