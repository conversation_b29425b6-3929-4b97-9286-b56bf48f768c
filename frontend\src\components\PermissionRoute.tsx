import React, { useState, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { usePermissionCheck } from '@/hooks/usePermissionCheck';
import { useAuth } from '@/contexts/AuthContext';
import { AlertTriangle, Lock, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface PermissionRouteProps {
  children: React.ReactNode;
  permission?: string;
  permissions?: string[];
  feature?: string;
  requireAll?: boolean;
  requireSuperuser?: boolean;
  fallbackPath?: string;
  showAccessDenied?: boolean;
}

/**
 * Route guard component that checks permissions before allowing access
 * Redirects to login or shows access denied based on authentication state
 */
export const PermissionRoute: React.FC<PermissionRouteProps> = ({
  children,
  permission,
  permissions,
  feature,
  requireAll = false,
  requireSuperuser = false,
  fallbackPath = '/login',
  showAccessDenied = true,
}) => {
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  const [isChecking, setIsChecking] = useState(true);
  const { 
    checkPermission, 
    checkFeatureAccess, 
    hasAnyPermission, 
    hasAllPermissions 
  } = usePermissionCheck();
  const { user, isAuthenticated, isLoading } = useAuth();
  const location = useLocation();

  useEffect(() => {
    const checkAccess = async () => {
      if (isLoading) return;

      if (!isAuthenticated || !user) {
        setHasAccess(false);
        setIsChecking(false);
        return;
      }

      // Superuser check
      if (requireSuperuser) {
        setHasAccess(user.is_superuser || false);
        setIsChecking(false);
        return;
      }

      // If superuser and no specific requirements, grant access
      if (user.is_superuser && !permission && !permissions && !feature) {
        setHasAccess(true);
        setIsChecking(false);
        return;
      }

      try {
        setIsChecking(true);
        let access = false;

        if (feature) {
          // Check feature access
          const featureAccess = await checkFeatureAccess(feature);
          access = featureAccess.canAccess;
        } else if (permission) {
          // Check single permission
          access = await checkPermission(permission);
        } else if (permissions && permissions.length > 0) {
          // Check multiple permissions
          if (requireAll) {
            access = await hasAllPermissions(permissions);
          } else {
            access = await hasAnyPermission(permissions);
          }
        } else {
          // No specific permissions required, allow access for authenticated users
          access = true;
        }

        setHasAccess(access);
      } catch (error) {
        console.error('Permission check failed:', error);
        setHasAccess(false);
      } finally {
        setIsChecking(false);
      }
    };

    checkAccess();
  }, [
    isLoading,
    isAuthenticated,
    user,
    permission,
    permissions,
    feature,
    requireAll,
    requireSuperuser,
    checkPermission,
    checkFeatureAccess,
    hasAnyPermission,
    hasAllPermissions,
  ]);

  // Show loading state
  if (isLoading || isChecking) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Checking permissions...</p>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to={fallbackPath} state={{ from: location }} replace />;
  }

  // Show access denied if no permission
  if (hasAccess === false) {
    if (!showAccessDenied) {
      return <Navigate to={fallbackPath} replace />;
    }

    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <Lock className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-xl font-semibold text-gray-900">
              Access Denied
            </CardTitle>
            <CardDescription className="text-gray-600">
              You don't have permission to access this page
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <div className="text-sm text-gray-500">
              {requireSuperuser && (
                <p className="flex items-center justify-center gap-2">
                  <AlertTriangle className="h-4 w-4" />
                  Superuser access required
                </p>
              )}
              {feature && (
                <p className="flex items-center justify-center gap-2">
                  <User className="h-4 w-4" />
                  Access to "{feature}" feature required
                </p>
              )}
              {permission && (
                <p className="flex items-center justify-center gap-2">
                  <User className="h-4 w-4" />
                  Permission "{permission}" required
                </p>
              )}
              {permissions && permissions.length > 0 && (
                <p className="flex items-center justify-center gap-2">
                  <User className="h-4 w-4" />
                  {requireAll ? 'All' : 'Any'} of these permissions required:
                  <br />
                  <span className="font-mono text-xs">
                    {permissions.join(', ')}
                  </span>
                </p>
              )}
            </div>
            <div className="flex gap-2 justify-center">
              <Button 
                variant="outline" 
                onClick={() => window.history.back()}
              >
                Go Back
              </Button>
              <Button 
                onClick={() => window.location.href = '/dashboard'}
              >
                Go to Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Render children if access granted
  return <>{children}</>;
};

interface AdminRouteProps {
  children: React.ReactNode;
  fallbackPath?: string;
}

/**
 * Route guard for admin-only pages
 */
export const AdminRoute: React.FC<AdminRouteProps> = ({ 
  children, 
  fallbackPath = '/dashboard' 
}) => {
  return (
    <PermissionRoute
      permissions={['auth.view_user', 'auth.add_user', 'auth.change_user']}
      requireAll={false}
      fallbackPath={fallbackPath}
    >
      {children}
    </PermissionRoute>
  );
};

interface SuperuserRouteProps {
  children: React.ReactNode;
  fallbackPath?: string;
}

/**
 * Route guard for superuser-only pages
 */
export const SuperuserRoute: React.FC<SuperuserRouteProps> = ({ 
  children, 
  fallbackPath = '/dashboard' 
}) => {
  return (
    <PermissionRoute
      requireSuperuser={true}
      fallbackPath={fallbackPath}
    >
      {children}
    </PermissionRoute>
  );
};

interface FeatureRouteProps {
  children: React.ReactNode;
  feature: string;
  action?: 'view' | 'add' | 'edit' | 'delete';
  fallbackPath?: string;
}

/**
 * Route guard for feature-based access
 */
export const FeatureRoute: React.FC<FeatureRouteProps> = ({ 
  children, 
  feature,
  action = 'view',
  fallbackPath = '/dashboard' 
}) => {
  return (
    <PermissionRoute
      feature={feature}
      fallbackPath={fallbackPath}
    >
      {children}
    </PermissionRoute>
  );
};

export default PermissionRoute;
