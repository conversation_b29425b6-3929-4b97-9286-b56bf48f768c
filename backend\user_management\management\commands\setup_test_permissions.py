from django.core.management.base import BaseCommand
from django.contrib.auth.models import User, Group, Permission
from django.contrib.contenttypes.models import ContentType
from setups.admission_type.models import AdmissionType
from setups.department.models import Department
from setups.program.models import Program


class Command(BaseCommand):
    help = 'Set up test user with specific permissions for testing the permission system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            default='service',
            help='Username for the test user (default: service)'
        )
        parser.add_argument(
            '--password',
            type=str,
            default='testpassword123',
            help='Password for the test user (default: testpassword123)'
        )

    def handle(self, *args, **options):
        username = options['username']
        password = options['password']

        self.stdout.write(
            self.style.SUCCESS(f'Setting up test user: {username}')
        )

        # Create or get the test user
        user, created = User.objects.get_or_create(
            username=username,
            defaults={
                'email': f'{username}@example.com',
                'first_name': 'Test',
                'last_name': 'User',
                'is_staff': True,
                'is_active': True,
            }
        )

        if created:
            user.set_password(password)
            user.save()
            self.stdout.write(
                self.style.SUCCESS(f'✓ Created user: {username}')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'✓ User already exists: {username}')
            )

        # Create groups and assign permissions
        groups_config = [
            {
                'name': 'Admission Type Managers',
                'description': 'Can manage admission types',
                'model': AdmissionType,
                'permissions': ['view', 'add', 'change', 'delete']
            },
            {
                'name': 'Department Managers', 
                'description': 'Can manage departments',
                'model': Department,
                'permissions': ['view', 'add', 'change', 'delete']
            },
            {
                'name': 'Program Managers',
                'description': 'Can manage programs', 
                'model': Program,
                'permissions': ['view', 'add', 'change', 'delete']
            }
        ]

        for group_config in groups_config:
            # Create group
            group, created = Group.objects.get_or_create(
                name=group_config['name']
            )
            
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'✓ Created group: {group.name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'✓ Group already exists: {group.name}')
                )

            # Get content type for the model
            content_type = ContentType.objects.get_for_model(group_config['model'])
            
            # Add permissions to group
            for perm_type in group_config['permissions']:
                permission_codename = f'{perm_type}_{content_type.model}'
                try:
                    permission = Permission.objects.get(
                        codename=permission_codename,
                        content_type=content_type
                    )
                    group.permissions.add(permission)
                    self.stdout.write(
                        f'  ✓ Added permission: {permission.content_type.app_label}.{permission.codename}'
                    )
                except Permission.DoesNotExist:
                    self.stdout.write(
                        self.style.ERROR(f'  ✗ Permission not found: {permission_codename}')
                    )

        # Assign user to specific group based on command line argument
        if '--admission-only' in args:
            # Only assign to admission type group
            admission_group = Group.objects.get(name='Admission Type Managers')
            user.groups.clear()
            user.groups.add(admission_group)
            self.stdout.write(
                self.style.SUCCESS(f'✓ Added {username} to Admission Type Managers group only')
            )
        else:
            # Add to all groups for testing
            for group_config in groups_config:
                group = Group.objects.get(name=group_config['name'])
                user.groups.add(group)
                self.stdout.write(
                    self.style.SUCCESS(f'✓ Added {username} to {group.name}')
                )

        # Display user permissions
        self.stdout.write('\n' + '='*50)
        self.stdout.write(f'User: {user.username}')
        self.stdout.write(f'Is superuser: {user.is_superuser}')
        self.stdout.write(f'Is staff: {user.is_staff}')
        self.stdout.write(f'Groups: {list(user.groups.values_list("name", flat=True))}')
        
        all_perms = user.get_all_permissions()
        self.stdout.write(f'\nAll permissions ({len(all_perms)}):')
        for perm in sorted(all_perms):
            self.stdout.write(f'  - {perm}')

        self.stdout.write('\n' + '='*50)
        self.stdout.write(
            self.style.SUCCESS('Setup completed! You can now test the permission system.')
        )
        self.stdout.write('\nNext steps:')
        self.stdout.write('1. Start the Django server: python manage.py runserver')
        self.stdout.write('2. Start the frontend: npm run dev')
        self.stdout.write(f'3. Login with username: {username}, password: {password}')
        self.stdout.write('4. Test that the user can only access permitted features')

    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            default='service',
            help='Username for the test user (default: service)'
        )
        parser.add_argument(
            '--password',
            type=str,
            default='testpassword123',
            help='Password for the test user (default: testpassword123)'
        )
        parser.add_argument(
            '--admission-only',
            action='store_true',
            help='Only assign admission type permissions (for testing specific access)'
        )
