#!/usr/bin/env python3
"""
Test script to verify the permission system is working correctly
This script tests the Django permission system integration with the frontend
"""

import os
import sys
import django
from django.conf import settings

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User, Group, Permission
from django.contrib.contenttypes.models import ContentType
from setups.admission_type.models import AdmissionType
from setups.department.models import Department
from setups.program.models import Program


def create_test_user_and_groups():
    """Create test user and groups for permission testing"""
    print("Creating test user and groups...")
    
    # Create test user
    user, created = User.objects.get_or_create(
        username='service',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Service',
            'last_name': 'User',
            'is_staff': True,
            'is_active': True,
        }
    )
    if created:
        user.set_password('testpassword123')
        user.save()
        print(f"✓ Created user: {user.username}")
    else:
        print(f"✓ User already exists: {user.username}")
    
    # Create admission type group
    admission_group, created = Group.objects.get_or_create(
        name='admission_type_managers'
    )
    if created:
        print(f"✓ Created group: {admission_group.name}")
    else:
        print(f"✓ Group already exists: {admission_group.name}")
    
    # Get admission type permissions
    admission_type_ct = ContentType.objects.get_for_model(AdmissionType)
    admission_permissions = Permission.objects.filter(content_type=admission_type_ct)
    
    # Add permissions to group
    for perm in admission_permissions:
        admission_group.permissions.add(perm)
        print(f"  ✓ Added permission: {perm.codename}")
    
    # Add user to group
    user.groups.add(admission_group)
    print(f"✓ Added user {user.username} to group {admission_group.name}")
    
    return user, admission_group


def test_user_permissions(user):
    """Test user permissions"""
    print(f"\nTesting permissions for user: {user.username}")
    print(f"Is superuser: {user.is_superuser}")
    print(f"Is staff: {user.is_staff}")
    print(f"Groups: {list(user.groups.values_list('name', flat=True))}")
    
    # Test specific permissions
    permissions_to_test = [
        'setups.view_admissiontype',
        'setups.add_admissiontype',
        'setups.change_admissiontype',
        'setups.delete_admissiontype',
        'setups.view_department',
        'setups.add_department',
        'auth.view_user',
        'auth.add_user',
    ]
    
    print("\nPermission test results:")
    for perm in permissions_to_test:
        has_perm = user.has_perm(perm)
        status = "✓" if has_perm else "✗"
        print(f"  {status} {perm}: {has_perm}")
    
    # Get all user permissions
    all_perms = user.get_all_permissions()
    print(f"\nAll user permissions ({len(all_perms)}):")
    for perm in sorted(all_perms):
        print(f"  - {perm}")


def test_middleware_mappings():
    """Test the middleware permission mappings"""
    print("\nTesting middleware permission mappings...")
    
    from user_management.middleware import PermissionBasedAccessMiddleware
    
    middleware = PermissionBasedAccessMiddleware(lambda x: x)
    
    test_cases = [
        ('/api/admission-types/', 'GET', ['setups.view_admissiontype']),
        ('/api/admission-types/', 'POST', ['setups.add_admissiontype']),
        ('/api/admission-types/1/', 'PUT', ['setups.change_admissiontype']),
        ('/api/admission-types/1/', 'DELETE', ['setups.delete_admissiontype']),
        ('/api/departments/', 'GET', ['setups.view_department']),
        ('/api/user/users/', 'GET', ['auth.view_user']),
    ]
    
    for path, method, expected_perms in test_cases:
        actual_perms = middleware._get_required_permissions(path, method)
        status = "✓" if actual_perms == expected_perms else "✗"
        print(f"  {status} {method} {path}: {actual_perms}")


def test_api_endpoints():
    """Test API endpoints with permission checking"""
    print("\nTesting API endpoints...")
    
    from django.test import Client
    from django.contrib.auth import authenticate
    
    client = Client()
    
    # Test without authentication
    response = client.get('/api/admission-types/')
    print(f"  Unauthenticated GET /api/admission-types/: {response.status_code}")
    
    # Test with authentication but no permissions
    user_no_perms = User.objects.create_user(
        username='noperms',
        password='testpass',
        is_staff=True
    )
    
    client.force_login(user_no_perms)
    response = client.get('/api/admission-types/')
    print(f"  No permissions GET /api/admission-types/: {response.status_code}")
    
    # Test with proper permissions
    user_with_perms, _ = create_test_user_and_groups()
    client.force_login(user_with_perms)
    response = client.get('/api/admission-types/')
    print(f"  With permissions GET /api/admission-types/: {response.status_code}")


def main():
    """Main test function"""
    print("=" * 60)
    print("DJANGO PERMISSION SYSTEM TEST")
    print("=" * 60)
    
    try:
        # Create test data
        user, group = create_test_user_and_groups()
        
        # Test user permissions
        test_user_permissions(user)
        
        # Test middleware mappings
        test_middleware_mappings()
        
        # Test API endpoints
        test_api_endpoints()
        
        print("\n" + "=" * 60)
        print("PERMISSION SYSTEM TEST COMPLETED")
        print("=" * 60)
        
        print("\nNext steps:")
        print("1. Start the Django server: python manage.py runserver")
        print("2. Login to Django admin with superuser account")
        print("3. Create a user 'service' and assign to 'admission_type_managers' group")
        print("4. Test frontend access with this user")
        print("5. Verify that the user can only access admission type features")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
