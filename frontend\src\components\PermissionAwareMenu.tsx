import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { MenuItem } from '@/config/menuConfig';
import { PermissionGuard, FeatureGuard } from '@/components/PermissionGuard';
import { useAuth } from '@/contexts/AuthContext';
import { cn } from '@/lib/utils';

interface PermissionAwareMenuProps {
  menuItems: MenuItem[];
  className?: string;
}

interface MenuItemComponentProps {
  item: MenuItem;
  index: number;
  activeSubmenu: number | null;
  setActiveSubmenu: (index: number | null) => void;
  onItemClick: (path: string, parentIndex?: number) => void;
}

const MenuItemComponent: React.FC<MenuItemComponentProps> = ({
  item,
  index,
  activeSubmenu,
  setActiveSubmenu,
  onItemClick,
}) => {
  const location = useLocation();
  const { user } = useAuth();

  // Debug logging for permission filtering
  if (item.requiredPermissions || item.requiredFeature || item.requireSuperuser) {
    console.log('MenuItemComponent permission check:', {
      title: item.title,
      user: user?.username,
      is_staff: user?.is_staff,
      is_superuser: user?.is_superuser,
      requiredPermissions: item.requiredPermissions,
      requiredFeature: item.requiredFeature,
      requireSuperuser: item.requireSuperuser,
      willShow: shouldShowItem()
    });
  }

  // Check if this menu item should be visible
  const shouldShowItem = () => {
    // Superuser can see everything
    if (user?.is_superuser) return true;

    // If requires superuser and user is not superuser, hide
    if (item.requireSuperuser && !user?.is_superuser) return false;

    // If no specific requirements, show to all staff
    if (!item.requiredPermissions && !item.requiredFeature) return true;

    // Let PermissionGuard or FeatureGuard handle the rest
    return true;
  };

  if (!shouldShowItem()) {
    return null;
  }

  const renderMenuItem = () => {
    if (item.submenu && item.items) {
      return (
        <div key={index}>
          <button
            onClick={() => setActiveSubmenu(activeSubmenu === index ? null : index)}
            className={cn(
              "w-full flex items-center justify-between px-4 py-3 text-left text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",
              activeSubmenu === index && "bg-gray-700 text-white"
            )}
          >
            <div className="flex items-center space-x-3">
              {React.createElement(item.icon, { className: "h-5 w-5" })}
              <span className="font-medium">{item.title}</span>
            </div>
            {activeSubmenu === index ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </button>
          
          {activeSubmenu === index && (
            <div className="bg-gray-700">
              {item.items.map((subItem, subIndex) => (
                <SubmenuItem
                  key={subIndex}
                  item={subItem}
                  parentIndex={index}
                  onItemClick={onItemClick}
                />
              ))}
            </div>
          )}
        </div>
      );
    } else if (item.path) {
      const isActive = location.pathname === '/graduate-admin' && 
                     location.search === item.path.split('?')[1] ? `?${item.path.split('?')[1]}` : '';
      
      return (
        <button
          key={index}
          onClick={() => onItemClick(item.path!)}
          className={cn(
            "w-full flex items-center space-x-3 px-4 py-3 text-left text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200",
            isActive && "bg-blue-600 text-white"
          )}
        >
          {React.createElement(item.icon, { className: "h-5 w-5" })}
          <span className="font-medium">{item.title}</span>
        </button>
      );
    }
    
    return null;
  };

  // Wrap with appropriate permission guard
  if (item.requireSuperuser) {
    return user?.is_superuser ? renderMenuItem() : null;
  } else if (item.requiredFeature) {
    return (
      <FeatureGuard feature={item.requiredFeature} showLoading={false}>
        {renderMenuItem()}
      </FeatureGuard>
    );
  } else if (item.requiredPermissions) {
    return (
      <PermissionGuard permissions={item.requiredPermissions} showLoading={false}>
        {renderMenuItem()}
      </PermissionGuard>
    );
  } else {
    return renderMenuItem();
  }
};

interface SubmenuItemProps {
  item: MenuItem;
  parentIndex: number;
  onItemClick: (path: string, parentIndex?: number) => void;
}

const SubmenuItem: React.FC<SubmenuItemProps> = ({ item, parentIndex, onItemClick }) => {
  const location = useLocation();
  const { user } = useAuth();

  // Check if this submenu item should be visible
  const shouldShowItem = () => {
    // Superuser can see everything
    if (user?.is_superuser) return true;
    
    // If requires superuser and user is not superuser, hide
    if (item.requireSuperuser && !user?.is_superuser) return false;
    
    // If no specific requirements, show to all staff
    if (!item.requiredPermissions && !item.requiredFeature) return true;
    
    // Let PermissionGuard or FeatureGuard handle the rest
    return true;
  };

  if (!shouldShowItem() || !item.path) {
    return null;
  }

  const isActive = location.pathname === '/graduate-admin' && 
                   location.search === `?${item.path.split('?')[1]}`;

  const renderSubmenuItem = () => (
    <button
      onClick={() => onItemClick(item.path!, parentIndex)}
      className={cn(
        "w-full flex items-center space-x-3 px-8 py-2 text-left text-gray-400 hover:bg-gray-600 hover:text-white transition-colors duration-200",
        isActive && "bg-blue-600 text-white"
      )}
    >
      {React.createElement(item.icon, { className: "h-4 w-4" })}
      <span className="text-sm">{item.title}</span>
    </button>
  );

  // Wrap with appropriate permission guard
  if (item.requireSuperuser) {
    return user?.is_superuser ? renderSubmenuItem() : null;
  } else if (item.requiredFeature) {
    return (
      <FeatureGuard feature={item.requiredFeature} showLoading={false}>
        {renderSubmenuItem()}
      </FeatureGuard>
    );
  } else if (item.requiredPermissions) {
    return (
      <PermissionGuard permissions={item.requiredPermissions} showLoading={false}>
        {renderSubmenuItem()}
      </PermissionGuard>
    );
  } else {
    return renderSubmenuItem();
  }
};

export const PermissionAwareMenu: React.FC<PermissionAwareMenuProps> = ({
  menuItems,
  className,
}) => {
  const [activeSubmenu, setActiveSubmenu] = useState<number | null>(null);
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();

  // Set active submenu based on current location
  useEffect(() => {
    const currentPath = location.pathname + location.search;
    
    menuItems.forEach((item, index) => {
      if (item.submenu && item.items) {
        const hasActiveSubitem = item.items.some(subItem => 
          subItem.path && currentPath.includes(subItem.path.split('?')[1] || '')
        );
        if (hasActiveSubitem) {
          setActiveSubmenu(index);
        }
      }
    });
  }, [location, menuItems]);

  const handleItemClick = (path: string, parentIndex?: number) => {
    navigate(path);
    
    // If clicking on a submenu item, keep the parent menu open
    if (parentIndex !== undefined) {
      setActiveSubmenu(parentIndex);
    }
  };

  // Debug: Count visible items
  const visibleItems = menuItems.filter(item => {
    // Same logic as in MenuItemComponent.shouldShowItem()
    if (user?.is_superuser) return true;
    if (item.requireSuperuser && !user?.is_superuser) return false;
    if (!item.requiredPermissions && !item.requiredFeature) return true;
    return true; // Let PermissionGuard handle the rest
  });

  console.log('PermissionAwareMenu Debug:', {
    user: user?.username,
    is_superuser: user?.is_superuser,
    totalItems: menuItems.length,
    visibleItems: visibleItems.length,
    userPermissions: user?.permissions?.slice(0, 5) // First 5 permissions
  });

  return (
    <nav className={cn("space-y-1", className)}>
      {/* Debug info for non-superusers */}
      {!user?.is_superuser && (
        <div className="text-xs text-gray-500 p-2 border-b">
          Debug: User "{user?.username}" | Showing {visibleItems.length}/{menuItems.length} items
        </div>
      )}

      {menuItems.map((item, index) => (
        <MenuItemComponent
          key={index}
          item={item}
          index={index}
          activeSubmenu={activeSubmenu}
          setActiveSubmenu={setActiveSubmenu}
          onItemClick={handleItemClick}
        />
      ))}
    </nav>
  );
};

export default PermissionAwareMenu;
