import { useState, useEffect, useCallback } from 'react';
import { permissionCheckAPI } from '@/services/authAPI';
import { useAuth } from '@/contexts/AuthContext';

interface PermissionState {
  [permission: string]: boolean;
}

interface FeatureAccess {
  canAccess: boolean;
  permissions: Record<string, boolean>;
  canView: boolean;
  canAdd: boolean;
  canEdit: boolean;
  canDelete: boolean;
}

/**
 * Hook for checking user permissions against the backend
 * This ensures frontend permissions match Django's permission system
 */
export const usePermissionCheck = () => {
  const [permissionCache, setPermissionCache] = useState<PermissionState>({});
  const [featureCache, setFeatureCache] = useState<Record<string, FeatureAccess>>({});
  const [isLoading, setIsLoading] = useState(false);
  const { user, isAuthenticated } = useAuth();

  // Clear cache when user changes
  useEffect(() => {
    setPermissionCache({});
    setFeatureCache({});
  }, [user?.id]);

  /**
   * Check a single permission
   */
  const checkPermission = useCallback(async (permission: string): Promise<boolean> => {
    if (!isAuthenticated || !user) return false;
    
    // Check cache first
    if (permission in permissionCache) {
      return permissionCache[permission];
    }

    try {
      setIsLoading(true);
      const response = await permissionCheckAPI.checkPermission(permission);
      const hasPermission = response.data.has_permission || false;
      
      // Update cache
      setPermissionCache(prev => ({
        ...prev,
        [permission]: hasPermission
      }));
      
      return hasPermission;
    } catch (error) {
      console.error('Permission check failed:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user, permissionCache]);

  /**
   * Check multiple permissions at once
   */
  const checkPermissions = useCallback(async (permissions: string[]): Promise<Record<string, boolean>> => {
    if (!isAuthenticated || !user) {
      return permissions.reduce((acc, perm) => ({ ...acc, [perm]: false }), {});
    }

    // Check which permissions are not in cache
    const uncachedPermissions = permissions.filter(perm => !(perm in permissionCache));
    
    if (uncachedPermissions.length === 0) {
      // All permissions are cached
      return permissions.reduce((acc, perm) => ({
        ...acc,
        [perm]: permissionCache[perm]
      }), {});
    }

    try {
      setIsLoading(true);
      const newPermissions = await permissionCheckAPI.checkPermissions(uncachedPermissions);
      
      // Update cache
      setPermissionCache(prev => ({
        ...prev,
        ...newPermissions
      }));
      
      // Return all requested permissions
      return permissions.reduce((acc, perm) => ({
        ...acc,
        [perm]: newPermissions[perm] ?? permissionCache[perm] ?? false
      }), {});
    } catch (error) {
      console.error('Permissions check failed:', error);
      return permissions.reduce((acc, perm) => ({ ...acc, [perm]: false }), {});
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user, permissionCache]);

  /**
   * Check feature access (includes CRUD permissions)
   */
  const checkFeatureAccess = useCallback(async (feature: string): Promise<FeatureAccess> => {
    if (!isAuthenticated || !user) {
      return {
        canAccess: false,
        permissions: {},
        canView: false,
        canAdd: false,
        canEdit: false,
        canDelete: false,
      };
    }

    // Check cache first
    if (feature in featureCache) {
      return featureCache[feature];
    }

    try {
      setIsLoading(true);
      const featureAccess = await permissionCheckAPI.checkFeatureAccess(feature);
      
      // Update cache
      setFeatureCache(prev => ({
        ...prev,
        [feature]: featureAccess
      }));
      
      return featureAccess;
    } catch (error) {
      console.error('Feature access check failed:', error);
      return {
        canAccess: false,
        permissions: {},
        canView: false,
        canAdd: false,
        canEdit: false,
        canDelete: false,
      };
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user, featureCache]);

  /**
   * Check if user has any of the specified permissions
   */
  const hasAnyPermission = useCallback(async (permissions: string[]): Promise<boolean> => {
    const results = await checkPermissions(permissions);
    return Object.values(results).some(hasPermission => hasPermission);
  }, [checkPermissions]);

  /**
   * Check if user has all of the specified permissions
   */
  const hasAllPermissions = useCallback(async (permissions: string[]): Promise<boolean> => {
    const results = await checkPermissions(permissions);
    return Object.values(results).every(hasPermission => hasPermission);
  }, [checkPermissions]);

  /**
   * Synchronous permission check (uses cache only)
   */
  const hasPermissionSync = useCallback((permission: string): boolean => {
    return permissionCache[permission] ?? false;
  }, [permissionCache]);

  /**
   * Synchronous feature access check (uses cache only)
   */
  const hasFeatureAccessSync = useCallback((feature: string): FeatureAccess => {
    return featureCache[feature] ?? {
      canAccess: false,
      permissions: {},
      canView: false,
      canAdd: false,
      canEdit: false,
      canDelete: false,
    };
  }, [featureCache]);

  /**
   * Clear permission cache
   */
  const clearCache = useCallback(() => {
    setPermissionCache({});
    setFeatureCache({});
  }, []);

  return {
    // Async methods
    checkPermission,
    checkPermissions,
    checkFeatureAccess,
    hasAnyPermission,
    hasAllPermissions,
    
    // Sync methods (cache-based)
    hasPermissionSync,
    hasFeatureAccessSync,
    
    // State
    isLoading,
    permissionCache,
    featureCache,
    
    // Utilities
    clearCache,
  };
};

export default usePermissionCheck;
