import { useState, useEffect, useCallback } from 'react';
import { permissionCheckAPI } from '@/services/authAPI';
import { useAuth } from '@/contexts/AuthContext';

interface PermissionState {
  [permission: string]: boolean;
}

interface FeatureAccess {
  canAccess: boolean;
  permissions: Record<string, boolean>;
  canView: boolean;
  canAdd: boolean;
  canEdit: boolean;
  canDelete: boolean;
}

// Global cache to prevent multiple API calls
let globalPermissionCache: PermissionState = {};
let globalFeatureCache: Record<string, FeatureAccess> = {};
let globalCacheTimestamp: number = 0;
let pendingPermissionChecks: Record<string, Promise<boolean>> = {};
let pendingFeatureChecks: Record<string, Promise<FeatureAccess>> = {};
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Rate limiter to prevent too many simultaneous requests
class PermissionRateLimiter {
  private requestQueue: Array<() => Promise<any>> = [];
  private activeRequests = 0;
  private maxConcurrent = 3;
  private requestDelay = 100; // ms between requests

  async execute<T>(requestFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.requestQueue.push(async () => {
        try {
          const result = await requestFn();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });
      this.processQueue();
    });
  }

  private async processQueue() {
    if (this.activeRequests >= this.maxConcurrent || this.requestQueue.length === 0) {
      return;
    }

    this.activeRequests++;
    const request = this.requestQueue.shift();

    if (request) {
      try {
        await request();
      } finally {
        this.activeRequests--;
        // Add delay between requests
        setTimeout(() => this.processQueue(), this.requestDelay);
      }
    }
  }
}

const rateLimiter = new PermissionRateLimiter();

/**
 * Hook for checking user permissions against the backend
 * This ensures frontend permissions match Django's permission system
 */
export const usePermissionCheck = () => {
  const [permissionCache, setPermissionCache] = useState<PermissionState>({});
  const [featureCache, setFeatureCache] = useState<Record<string, FeatureAccess>>({});
  const [isLoading, setIsLoading] = useState(false);
  const { user, isAuthenticated } = useAuth();

  // Clear cache when user changes
  useEffect(() => {
    // Clear both local and global caches
    setPermissionCache({});
    setFeatureCache({});
    globalPermissionCache = {};
    globalFeatureCache = {};
    globalCacheTimestamp = 0;
    // Clear any pending requests
    Object.keys(pendingPermissionChecks).forEach(key => delete pendingPermissionChecks[key]);
    Object.keys(pendingFeatureChecks).forEach(key => delete pendingFeatureChecks[key]);
  }, [user?.id]);

  /**
   * Check a single permission with global caching and rate limiting
   */
  const checkPermission = useCallback(async (permission: string): Promise<boolean> => {
    if (!isAuthenticated || !user) return false;

    // Check global cache first
    const now = Date.now();
    if (permission in globalPermissionCache && (now - globalCacheTimestamp) < CACHE_DURATION) {
      return globalPermissionCache[permission];
    }

    // Check if there's already a pending request for this permission
    if (pendingPermissionChecks[permission]) {
      return pendingPermissionChecks[permission];
    }

    // Create a new request with rate limiting
    pendingPermissionChecks[permission] = rateLimiter.execute(async () => {
      try {
        setIsLoading(true);
        const response = await permissionCheckAPI.checkPermission(permission);
        const hasPermission = response.data.has_permission || false;

        // Update global cache
        globalPermissionCache[permission] = hasPermission;
        globalCacheTimestamp = now;

        // Update local cache
        setPermissionCache(prev => ({
          ...prev,
          [permission]: hasPermission
        }));

        return hasPermission;
      } catch (error) {
        console.error('Permission check failed:', error);
        globalPermissionCache[permission] = false;
        return false;
      } finally {
        setIsLoading(false);
        delete pendingPermissionChecks[permission];
      }
    });

    return pendingPermissionChecks[permission];
  }, [isAuthenticated, user]);

  /**
   * Check multiple permissions at once
   */
  const checkPermissions = useCallback(async (permissions: string[]): Promise<Record<string, boolean>> => {
    if (!isAuthenticated || !user) {
      return permissions.reduce((acc, perm) => ({ ...acc, [perm]: false }), {});
    }

    // Check which permissions are not in cache
    const uncachedPermissions = permissions.filter(perm => !(perm in permissionCache));
    
    if (uncachedPermissions.length === 0) {
      // All permissions are cached
      return permissions.reduce((acc, perm) => ({
        ...acc,
        [perm]: permissionCache[perm]
      }), {});
    }

    try {
      setIsLoading(true);
      const newPermissions = await permissionCheckAPI.checkPermissions(uncachedPermissions);
      
      // Update cache
      setPermissionCache(prev => ({
        ...prev,
        ...newPermissions
      }));
      
      // Return all requested permissions
      return permissions.reduce((acc, perm) => ({
        ...acc,
        [perm]: newPermissions[perm] ?? permissionCache[perm] ?? false
      }), {});
    } catch (error) {
      console.error('Permissions check failed:', error);
      return permissions.reduce((acc, perm) => ({ ...acc, [perm]: false }), {});
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user, permissionCache]);

  /**
   * Check feature access (includes CRUD permissions)
   */
  const checkFeatureAccess = useCallback(async (feature: string): Promise<FeatureAccess> => {
    if (!isAuthenticated || !user) {
      return {
        canAccess: false,
        permissions: {},
        canView: false,
        canAdd: false,
        canEdit: false,
        canDelete: false,
      };
    }

    // Check global cache first
    const now = Date.now();
    if (feature in globalFeatureCache && (now - globalCacheTimestamp) < CACHE_DURATION) {
      return globalFeatureCache[feature];
    }

    // Check if there's already a pending request for this feature
    if (pendingFeatureChecks[feature]) {
      return pendingFeatureChecks[feature];
    }

    // Create a new request with rate limiting
    pendingFeatureChecks[feature] = rateLimiter.execute(async () => {
      try {
        setIsLoading(true);
        const featureAccess = await permissionCheckAPI.checkFeatureAccess(feature);

        // Update global cache
        globalFeatureCache[feature] = featureAccess;
        globalCacheTimestamp = now;

        // Update local cache
        setFeatureCache(prev => ({
          ...prev,
          [feature]: featureAccess
        }));

        return featureAccess;
      } catch (error) {
        console.error('Feature access check failed:', error);
        const fallbackAccess = {
          canAccess: false,
          permissions: {},
          canView: false,
          canAdd: false,
          canEdit: false,
          canDelete: false,
        };
        globalFeatureCache[feature] = fallbackAccess;
        return fallbackAccess;
      } finally {
        setIsLoading(false);
        delete pendingFeatureChecks[feature];
      }
    });

    return pendingFeatureChecks[feature];
  }, [isAuthenticated, user]);

  /**
   * Check if user has any of the specified permissions
   */
  const hasAnyPermission = useCallback(async (permissions: string[]): Promise<boolean> => {
    const results = await checkPermissions(permissions);
    return Object.values(results).some(hasPermission => hasPermission);
  }, [checkPermissions]);

  /**
   * Check if user has all of the specified permissions
   */
  const hasAllPermissions = useCallback(async (permissions: string[]): Promise<boolean> => {
    const results = await checkPermissions(permissions);
    return Object.values(results).every(hasPermission => hasPermission);
  }, [checkPermissions]);

  /**
   * Synchronous permission check (uses cache only)
   */
  const hasPermissionSync = useCallback((permission: string): boolean => {
    return permissionCache[permission] ?? false;
  }, [permissionCache]);

  /**
   * Synchronous feature access check (uses cache only)
   */
  const hasFeatureAccessSync = useCallback((feature: string): FeatureAccess => {
    return featureCache[feature] ?? {
      canAccess: false,
      permissions: {},
      canView: false,
      canAdd: false,
      canEdit: false,
      canDelete: false,
    };
  }, [featureCache]);

  /**
   * Clear permission cache
   */
  const clearCache = useCallback(() => {
    // Clear both local and global caches
    setPermissionCache({});
    setFeatureCache({});
    globalPermissionCache = {};
    globalFeatureCache = {};
    globalCacheTimestamp = 0;
    // Clear any pending requests
    Object.keys(pendingPermissionChecks).forEach(key => delete pendingPermissionChecks[key]);
    Object.keys(pendingFeatureChecks).forEach(key => delete pendingFeatureChecks[key]);
  }, []);

  return {
    // Async methods
    checkPermission,
    checkPermissions,
    checkFeatureAccess,
    hasAnyPermission,
    hasAllPermissions,
    
    // Sync methods (cache-based)
    hasPermissionSync,
    hasFeatureAccessSync,
    
    // State
    isLoading,
    permissionCache,
    featureCache,
    
    // Utilities
    clearCache,
  };
};

export default usePermissionCheck;
