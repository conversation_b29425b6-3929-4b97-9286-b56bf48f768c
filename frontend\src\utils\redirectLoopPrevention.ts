/**
 * Utility to prevent infinite redirect loops in authentication
 */

const REDIRECT_COOLDOWN = 3000; // 3 seconds
const MAX_REDIRECTS = 3;

interface RedirectTracker {
  count: number;
  lastRedirect: number;
  path: string;
}

class RedirectLoopPrevention {
  private static instance: RedirectLoopPrevention;
  private redirectHistory: Map<string, RedirectTracker> = new Map();

  static getInstance(): RedirectLoopPrevention {
    if (!RedirectLoopPrevention.instance) {
      RedirectLoopPrevention.instance = new RedirectLoopPrevention();
    }
    return RedirectLoopPrevention.instance;
  }

  /**
   * Check if a redirect should be allowed
   */
  canRedirect(fromPath: string, toPath: string): boolean {
    const key = `${fromPath}->${toPath}`;
    const now = Date.now();
    const tracker = this.redirectHistory.get(key);

    // Debug logging
    console.log(`RedirectLoopPrevention: Checking redirect ${key}`, {
      tracker,
      now,
      cooldownExpired: tracker ? (now - tracker.lastRedirect > REDIRECT_COOLDOWN) : 'N/A'
    });

    if (!tracker) {
      // First redirect, allow it
      console.log(`RedirectLoopPrevention: First redirect ${key} - ALLOWED`);
      this.redirectHistory.set(key, {
        count: 1,
        lastRedirect: now,
        path: key
      });
      return true;
    }

    // Check if enough time has passed
    if (now - tracker.lastRedirect > REDIRECT_COOLDOWN) {
      // Reset counter after cooldown
      console.log(`RedirectLoopPrevention: Cooldown expired for ${key} - ALLOWED (resetting counter)`);
      tracker.count = 1;
      tracker.lastRedirect = now;
      return true;
    }

    // Check if we've exceeded max redirects
    if (tracker.count >= MAX_REDIRECTS) {
      console.error(`Redirect loop detected: ${key}. Blocking redirect.`);
      console.error('Current redirect history:', this.getStatus());
      return false;
    }

    // Increment counter and allow redirect
    tracker.count++;
    tracker.lastRedirect = now;
    console.log(`RedirectLoopPrevention: Redirect ${key} - ALLOWED (count: ${tracker.count})`);
    return true;
  }

  /**
   * Clear redirect history for a specific path
   */
  clearPath(path: string): void {
    for (const [key] of this.redirectHistory) {
      if (key.includes(path)) {
        this.redirectHistory.delete(key);
      }
    }
  }

  /**
   * Clear all redirect history
   */
  clearAll(): void {
    this.redirectHistory.clear();
  }

  /**
   * Get current redirect status for debugging
   */
  getStatus(): Record<string, RedirectTracker> {
    const status: Record<string, RedirectTracker> = {};
    for (const [key, value] of this.redirectHistory) {
      status[key] = { ...value };
    }
    return status;
  }
}

export const redirectLoopPrevention = RedirectLoopPrevention.getInstance();

/**
 * Hook to use redirect loop prevention
 */
export const useRedirectLoopPrevention = () => {
  return {
    canRedirect: redirectLoopPrevention.canRedirect.bind(redirectLoopPrevention),
    clearPath: redirectLoopPrevention.clearPath.bind(redirectLoopPrevention),
    clearAll: redirectLoopPrevention.clearAll.bind(redirectLoopPrevention),
    getStatus: redirectLoopPrevention.getStatus.bind(redirectLoopPrevention),
  };
};

export default redirectLoopPrevention;
