from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth.models import AnonymousUser
from django.core.cache import cache
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
import logging
import re

logger = logging.getLogger(__name__)


def clear_user_cache(user_id):
    """Clear cached roles and permissions for a user"""
    cache.delete(f"user_roles_{user_id}")
    cache.delete(f"user_permissions_{user_id}")


def clear_all_user_cache():
    """Clear all user role and permission caches"""
    # This is a simple implementation - in production you might want to use cache versioning
    cache.clear()


class PermissionBasedAccessMiddleware(MiddlewareMixin):
    """
    Enhanced middleware to enforce Django permissions on API endpoints
    Maps URL patterns to required permissions and checks user access
    """

    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)

        # Define URL pattern to permission mappings
        self.permission_mappings = {
            # Admission Type endpoints
            r'^/api/admission-types/$': {
                'GET': ['setups.view_admissiontype'],
                'POST': ['setups.add_admissiontype'],
            },
            r'^/api/admission-types/\d+/$': {
                'GET': ['setups.view_admissiontype'],
                'PUT': ['setups.change_admissiontype'],
                'PATCH': ['setups.change_admissiontype'],
                'DELETE': ['setups.delete_admissiontype'],
            },

            # Department endpoints
            r'^/api/departments/$': {
                'GET': ['setups.view_department'],
                'POST': ['setups.add_department'],
            },
            r'^/api/departments/\d+/$': {
                'GET': ['setups.view_department'],
                'PUT': ['setups.change_department'],
                'PATCH': ['setups.change_department'],
                'DELETE': ['setups.delete_department'],
            },

            # Program endpoints
            r'^/api/programs/$': {
                'GET': ['setups.view_program'],
                'POST': ['setups.add_program'],
            },
            r'^/api/programs/\d+/$': {
                'GET': ['setups.view_program'],
                'PUT': ['setups.change_program'],
                'PATCH': ['setups.change_program'],
                'DELETE': ['setups.delete_program'],
            },

            # College endpoints
            r'^/api/colleges/$': {
                'GET': ['setups.view_college'],
                'POST': ['setups.add_college'],
            },
            r'^/api/colleges/\d+/$': {
                'GET': ['setups.view_college'],
                'PUT': ['setups.change_college'],
                'PATCH': ['setups.change_college'],
                'DELETE': ['setups.delete_college'],
            },

            # User Management endpoints
            r'^/api/user/users/$': {
                'GET': ['auth.view_user'],
                'POST': ['auth.add_user'],
            },
            r'^/api/user/users/\d+/$': {
                'GET': ['auth.view_user'],
                'PUT': ['auth.change_user'],
                'PATCH': ['auth.change_user'],
                'DELETE': ['auth.delete_user'],
            },

            # Role Management endpoints
            r'^/api/user/roles/$': {
                'GET': ['auth.view_group'],
                'POST': ['auth.add_group'],
            },
            r'^/api/user/roles/\d+/$': {
                'GET': ['auth.view_group'],
                'PUT': ['auth.change_group'],
                'PATCH': ['auth.change_group'],
                'DELETE': ['auth.delete_group'],
            },

            # Alumni Applications (admin access)
            r'^/api/alumni-applications/$': {
                'GET': ['alumni_applications.view_alumniapplication'],
                'POST': None,  # Allow public creation
            },
            r'^/api/alumni-applications/\d+/$': {
                'GET': ['alumni_applications.view_alumniapplication'],
                'PUT': ['alumni_applications.change_alumniapplication'],
                'PATCH': ['alumni_applications.change_alumniapplication'],
                'DELETE': ['alumni_applications.delete_alumniapplication'],
            },
        }

    def process_request(self, request):
        # Skip for anonymous users on public endpoints
        if not request.user.is_authenticated:
            return None

        # Skip for superusers
        if request.user.is_superuser:
            return None

        # Check if this is an API endpoint that needs permission checking
        if not request.path_info.startswith('/api/'):
            return None

        # Find matching URL pattern and required permissions
        required_permissions = self._get_required_permissions(request.path_info, request.method)

        if required_permissions is None:
            # No permissions required (public endpoint)
            return None

        if not required_permissions:
            # Empty list means no specific permissions needed but must be authenticated
            return None

        # Check if user has the required permissions
        if not self._user_has_permissions(request.user, required_permissions):
            logger.warning(
                f"Permission denied: User {request.user.username} "
                f"attempted {request.method} {request.path_info} "
                f"but lacks permissions: {required_permissions}"
            )

            return JsonResponse({
                'error': 'Permission denied',
                'message': 'You do not have permission to perform this action',
                'required_permissions': required_permissions,
                'user_permissions': list(request.user.get_all_permissions())
            }, status=403)

        return None

    def _get_required_permissions(self, path, method):
        """Get required permissions for a given path and HTTP method"""
        for pattern, methods in self.permission_mappings.items():
            if re.match(pattern, path):
                return methods.get(method, [])
        return []  # No specific permissions required

    def _user_has_permissions(self, user, required_permissions):
        """Check if user has all required permissions"""
        if not required_permissions:
            return True

        return all(user.has_perm(perm) for perm in required_permissions)


class RBACMiddleware(MiddlewareMixin):
    """
    Middleware to add role-based access control information to requests
    """

    def process_request(self, request):
        # Add RBAC helper methods to request object
        request.has_role = lambda role_name: self._has_role(request.user, role_name)
        request.has_any_role = lambda role_names: self._has_any_role(request.user, role_names)
        request.has_permission = lambda perm: self._has_permission(request.user, perm)
        request.get_user_roles = lambda: self._get_user_roles(request.user)
        request.get_user_permissions = lambda: self._get_user_permissions(request.user)

        return None

    def _has_role(self, user, role_name):
        """Check if user has a specific role"""
        if not user or isinstance(user, AnonymousUser):
            return False
        return user.groups.filter(name=role_name).exists()

    def _has_any_role(self, user, role_names):
        """Check if user has any of the specified roles"""
        if not user or isinstance(user, AnonymousUser):
            return False
        return user.groups.filter(name__in=role_names).exists()

    def _has_permission(self, user, permission_codename):
        """Check if user has a specific permission"""
        if not user or isinstance(user, AnonymousUser):
            return False
        return user.has_perm(permission_codename)

    def _get_user_roles(self, user):
        """Get all roles for a user with caching"""
        if not user or isinstance(user, AnonymousUser):
            return []

        cache_key = f"user_roles_{user.id}"
        roles = cache.get(cache_key)

        if roles is None:
            roles = list(user.groups.values_list('name', flat=True))
            cache.set(cache_key, roles, 300)  # Cache for 5 minutes

        return roles

    def _get_user_permissions(self, user):
        """Get all permissions for a user with caching"""
        if not user or isinstance(user, AnonymousUser):
            return []

        cache_key = f"user_permissions_{user.id}"
        permissions = cache.get(cache_key)

        if permissions is None:
            permissions = list(user.get_all_permissions())
            cache.set(cache_key, permissions, 300)  # Cache for 5 minutes

        return permissions


class JWTRoleMiddleware(MiddlewareMixin):
    """
    Middleware to extract and validate JWT tokens and add role information
    """
    
    def process_request(self, request):
        # Skip for certain paths
        skip_paths = ['/api/token/', '/api/user/register/', '/admin/', '/swagger/', '/redoc/']
        if any(request.path.startswith(path) for path in skip_paths):
            return None

        # Try to authenticate using JWT
        jwt_auth = JWTAuthentication()
        try:
            auth_result = jwt_auth.authenticate(request)
            if auth_result:
                user, token = auth_result
                request.user = user
                request.auth = token
                
                # Add role information to request
                if hasattr(user, 'profile'):
                    request.user_roles = list(user.groups.values_list('name', flat=True))
                    request.user_permissions = list(user.get_all_permissions())
                    request.user_department = getattr(user.profile, 'department', None)
                else:
                    request.user_roles = []
                    request.user_permissions = []
                    request.user_department = None
                    
        except (InvalidToken, TokenError) as e:
            logger.warning(f"JWT authentication failed: {str(e)}")
            # Don't block the request, let the view handle authentication
            pass
        except Exception as e:
            logger.error(f"Unexpected error in JWT middleware: {str(e)}")
            pass

        return None


class RoleBasedAccessMiddleware(MiddlewareMixin):
    """
    Middleware to enforce role-based access control at the URL level
    """
    
    # Define URL patterns and required roles
    PROTECTED_URLS = {
        '/api/admin/': ['Admin', 'Super Admin'],
        '/api/user/users/': ['Admin', 'Super Admin', 'Manager'],
        '/api/user/roles/': ['Admin', 'Super Admin'],
        '/api/user/permissions/': ['Admin', 'Super Admin'],
        '/api/official/': ['Admin', 'Super Admin', 'Manager', 'Staff'],
        '/api/registration/staff/': ['Admin', 'Super Admin', 'Manager', 'Staff'],
    }

    def process_request(self, request):
        # Skip for non-API requests
        if not request.path.startswith('/api/'):
            return None

        # Check if URL requires specific roles
        for url_pattern, required_roles in self.PROTECTED_URLS.items():
            if request.path.startswith(url_pattern):
                if not self._check_access(request, required_roles):
                    return JsonResponse(
                        {'error': 'Insufficient permissions', 'required_roles': required_roles},
                        status=403
                    )
                break

        return None

    def _check_access(self, request, required_roles):
        """Check if user has access based on required roles"""
        # Allow unauthenticated requests to pass through (let views handle auth)
        if not hasattr(request, 'user') or isinstance(request.user, AnonymousUser):
            return False

        # Allow superusers
        if request.user.is_superuser:
            return True

        # Check if user has any of the required roles
        user_roles = list(request.user.groups.values_list('name', flat=True))
        return any(role in user_roles for role in required_roles)


class AuditLogMiddleware(MiddlewareMixin):
    """
    Middleware to log user actions for audit purposes
    """
    
    def process_request(self, request):
        # Log important actions
        if request.method in ['POST', 'PUT', 'PATCH', 'DELETE']:
            user_id = getattr(request.user, 'id', None) if hasattr(request, 'user') else None
            username = getattr(request.user, 'username', 'Anonymous') if hasattr(request, 'user') else 'Anonymous'
            
            logger.info(f"User Action: {username} ({user_id}) - {request.method} {request.path}")
            
            # Store audit info in request for views to use
            request.audit_info = {
                'user_id': user_id,
                'username': username,
                'method': request.method,
                'path': request.path,
                'ip_address': self._get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            }

        return None

    def _get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class SessionTimeoutMiddleware(MiddlewareMixin):
    """
    Middleware to handle session timeout for enhanced security
    """
    
    def process_request(self, request):
        # This can be used to implement session timeout logic
        # For JWT tokens, timeout is handled by token expiration
        # But we can add additional checks here if needed
        
        if hasattr(request, 'user') and request.user.is_authenticated:
            # Update last activity timestamp
            from django.utils import timezone
            if hasattr(request.user, 'profile'):
                # You could add a last_activity field to UserProfile if needed
                pass
        
        return None
