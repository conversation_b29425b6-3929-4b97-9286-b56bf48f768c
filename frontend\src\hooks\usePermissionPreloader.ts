import { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissionCheck } from '@/hooks/usePermissionCheck';

/**
 * Hook to preload common permissions to reduce API calls
 * This should be used in the main layout components
 */
export const usePermissionPreloader = () => {
  const { user, isAuthenticated } = useAuth();
  const { checkFeatureAccess, checkPermissions } = usePermissionCheck();

  useEffect(() => {
    if (!isAuthenticated || !user) return;

    // Preload common features that are likely to be checked
    const commonFeatures = [
      'admission-types',
      'departments',
      'programs',
      'colleges',
      'users',
      'roles',
      'alumni-applications',
      'service-types',
      'document-types',
      'certificate-types'
    ];

    // Preload common permissions
    const commonPermissions = [
      'admission_type.view_admissiontype',
      'admission_type.add_admissiontype',
      'admission_type.change_admissiontype',
      'admission_type.delete_admissiontype',
      'department.view_department',
      'department.add_department',
      'department.change_department',
      'department.delete_department',
      'program.view_program',
      'program.add_program',
      'program.change_program',
      'program.delete_program',
      'college.view_college',
      'college.add_college',
      'college.change_college',
      'college.delete_college',
      'auth.view_user',
      'auth.add_user',
      'auth.change_user',
      'auth.delete_user'
    ];

    // Preload features in batches to avoid overwhelming the API
    const preloadFeatures = async () => {
      try {
        // Load features in small batches with delays
        for (let i = 0; i < commonFeatures.length; i += 3) {
          const batch = commonFeatures.slice(i, i + 3);
          await Promise.all(batch.map(feature => 
            checkFeatureAccess(feature).catch(err => {
              console.warn(`Failed to preload feature ${feature}:`, err);
            })
          ));
          
          // Small delay between batches
          if (i + 3 < commonFeatures.length) {
            await new Promise(resolve => setTimeout(resolve, 200));
          }
        }
      } catch (error) {
        console.warn('Error preloading features:', error);
      }
    };

    // Preload permissions in batches
    const preloadPermissions = async () => {
      try {
        // Load permissions in batches of 5
        for (let i = 0; i < commonPermissions.length; i += 5) {
          const batch = commonPermissions.slice(i, i + 5);
          await checkPermissions(batch).catch(err => {
            console.warn(`Failed to preload permissions batch:`, err);
          });
          
          // Small delay between batches
          if (i + 5 < commonPermissions.length) {
            await new Promise(resolve => setTimeout(resolve, 300));
          }
        }
      } catch (error) {
        console.warn('Error preloading permissions:', error);
      }
    };

    // Start preloading after a short delay to let the page load first
    const timer = setTimeout(() => {
      preloadFeatures();
      // Start permission preloading after feature preloading begins
      setTimeout(preloadPermissions, 1000);
    }, 2000);

    return () => clearTimeout(timer);
  }, [user?.id, isAuthenticated, checkFeatureAccess, checkPermissions]);
};

export default usePermissionPreloader;
